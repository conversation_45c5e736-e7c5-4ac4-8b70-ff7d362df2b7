services:
  whisperlivekit:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8001:8000"
    volumes:
      - ./:/app
      - huggingface_cache:/root/.cache/huggingface
      - torch_cache:/root/.cache/torch
      - pip_cache:/root/.cache/pip
    command: ["bash", "-c", "pip install -e . && whisperlivekit-server --host 0.0.0.0 --vac"]
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

volumes:
  huggingface_cache:
  torch_cache:
  pip_cache:
