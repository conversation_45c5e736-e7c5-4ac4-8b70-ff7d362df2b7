from dataclasses import dataclass
from typing import Optional
from decimal import Decimal

@dataclass
class TimedText:
    start: Optional[Decimal]
    end: Optional[Decimal]
    text: Optional[str] = ''
    speaker: Optional[int] = -1
    probability: Optional[float] = None
    is_dummy: Optional[bool] = False

@dataclass
class ASRToken(TimedText):
    def with_offset(self, offset: Decimal) -> "ASRToken":
        """Return a new token with the time offset added."""
        return ASRToken(self.start + offset, self.end + offset, self.text, self.speaker, self.probability)

@dataclass
class Sentence(TimedText):
    pass

@dataclass
class Transcript(TimedText):
    pass

@dataclass
class SpeakerSegment(TimedText):
    pass